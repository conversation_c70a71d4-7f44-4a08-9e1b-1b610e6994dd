// Data transformation and import script
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://hirxwnqhiwfolflyqxcq.supabase.co';
const supabaseKey = process.env.SUPABASE_ANON_KEY; // You'll need to set this

const supabase = createClient(supabaseUrl, supabaseKey);

// Raw data extracted from HTML
const rawData = [
  {
    client: "ESGold",
    topic: "Web2", 
    type: "69X Minutes",
    date: "13/07/2025",
    title: "🚨69 𝕏 MINUTES w/ TRUMP FAMILY, RAND PAUL & SEC HEGSETH: SHAKING UP THE SWAMP | EP. 12",
    url: "https://x.com/MarioNawfal/status/1944501674422489406",
    report: "https://drive.google.com/file/d/1Dv0z9wynuM4Rvw0CG_CSXON2R5nevZew/view",
    views: "-",
    impressions: "1.1 M",
    follows: "~400"
  },
  {
    client: "Nodepay",
    topic: "AI",
    type: "Video on RT", 
    date: "27/11/2024",
    title: "The Decentralized Rise of Memecoins! #CryptoDaily w/@nodepay_ai",
    url: "https://x.com/i/broadcasts/1yoJMyjqPmYJQ",
    report: "https://drive.google.com/file/d/1fardX3jdul2N3ysdhpoPZYu17yi8KZce/view",
    views: "106.5K",
    impressions: "323.2K", 
    follows: "14000"
  },
  {
    client: "Lumerin Protocol",
    topic: "AI",
    type: "Video on RT",
    date: "02/12/2024", 
    title: "Bitcoin Hashrate Hits ATH! #CryptoDaily w/@HelloLumerin",
    url: "https://x.com/i/broadcasts/1RDGlyvpAVEJL",
    report: "https://drive.google.com/file/d/1DEdrx7m2zUQJ9NV7MvaOy9pwpKkzm1l_/view?usp=drive_link",
    views: "103.1k",
    impressions: "310.7K",
    follows: "800"
  },
  {
    client: "Neurochain",
    topic: "AI", 
    type: "Video on RT",
    date: "03/12/2024",
    title: "AI in Crypto: The Hot Trend of 2024 #CryptoDaily w/@NeurochainAI",
    url: "https://x.com/i/broadcasts/1LyxBgAwPyoKN",
    report: "https://drive.google.com/file/d/1h5iLpMkg4N-7-_A2WiA9fo_mwajScsR8/view?usp=drive_link",
    views: "126K",
    impressions: "305.3K",
    follows: "500"
  },
  {
    client: "Occupy",
    topic: "AI",
    type: "Video on M",
    date: "04/12/2024",
    title: "The State of Solana Ecosystem #CryptoDaily w/@OccupyMarsSOL", 
    url: "https://x.com/i/broadcasts/1PlKQbLXBqYGE",
    report: "https://drive.google.com/file/d/10zf77snR4lXUkgkw4LB1rRvlW_u0hiUo/view",
    views: "198.3K",
    impressions: "419.5K",
    follows: "765"
  },
  {
    client: "Zerebro",
    topic: "AI",
    type: "Video on M", 
    date: "16/12/2024",
    title: "Music, Crypto, and AI #CryptoDaily w/@0xzerebro",
    url: "https://x.com/i/broadcasts/1mnGeAQoXnoGX",
    report: "https://drive.google.com/file/d/1Sf5f88erFHa60sS3vMmMUmfoPo9LrvXm/view",
    views: "198.9K",
    impressions: "499.5K", 
    follows: "1,200"
  },
  {
    client: "KOLZ",
    topic: "AI",
    type: "Video on M",
    date: "04/12/2024",
    title: "AI and Digital Immortality #CryptoDaily w/@ChatKolz",
    url: "https://x.com/i/broadcasts/1YpJklvLapZxj", 
    report: "https://drive.google.com/file/d/1Y-39kezmpQjulyCtz38Zb-XxQcoX5Wh-/view",
    views: "189.2K",
    impressions: "477K",
    follows: "500"
  },
  {
    client: "AI Nexus",
    topic: "AI",
    type: "Video on M",
    date: "26/12/2024",
    title: "Will AI Supercharge Crypto Gaming? w/@_AI_Nexus",
    url: "https://x.com/i/broadcasts/1OyJAZEkMVOxb",
    report: "https://drive.google.com/file/d/1uW1ICeO-jbDPLzvgk9GaL_Hy6cGjiob-/view", 
    views: "162.1K",
    impressions: "494.4K",
    follows: "2,900"
  },
  {
    client: "Cosmic Factions",
    topic: "AI",
    type: "Video on RT",
    date: "29/1/2025",
    title: "Free AI Incoming – Are You Ready? w/@cosmic_factions",
    url: "https://x.com/i/broadcasts/1MYxNMlZZopJw",
    report: "https://drive.google.com/file/d/1PNsOMmA-1-iOOjAeHcPjBoAH5og7tnRr/view",
    views: "134.3K", 
    impressions: "401.2K",
    follows: "900"
  },
  {
    client: "OG Labs", 
    topic: "AI",
    type: "Video on RT",
    date: "17/2/2025",
    title: "Why Decentralized AI Could Outperform w/@0G_labs",
    url: "https://x.com/i/broadcasts/1mnGegMnEERxX",
    report: "https://drive.google.com/file/d/1gXXxnsk5hln07YerMPCCO3yEAmDUrxX4/view?usp=drive_link",
    views: "97.4K",
    impressions: "457.5K",
    follows: "5,300"
  },
  {
    client: "DAOBase",
    topic: "AI",
    type: "Video on RT", 
    date: "17/2/2025",
    title: "Onchain Communities: A Game Changer? w/@daobase_ai",
    url: "https://x.com/i/broadcasts/1ypJdZDMMedKW",
    report: "https://drive.google.com/file/d/1x7rsbmlqzkgc68iKXWyhi0paVxz4XgK8/view",
    views: "161K",
    impressions: "542.3K",
    follows: "1,000"
  },
  {
    client: "Pure Protocol",
    topic: "AI",
    type: "Video on RT",
    date: "17/2/2025", 
    title: "The Rise of AI-Deployed Memecoins w/@pureprotocol",
    url: "https://x.com/i/broadcasts/1YqKDZWlByVJV",
    report: "https://drive.google.com/file/d/1OmxqQFRiMWVf-BBxjLvUUBf2eway1GJi/view",
    views: "246.4K",
    impressions: "783.9K",
    follows: "386"
  },
  {
    client: "Aiden Labs",
    topic: "AI", 
    type: "Video on M",
    date: "4/4/2025",
    title: "Web3 in a Bear Market: What's Next? w/@Aiden_Labs",
    url: "https://x.com/i/broadcasts/1OyKALnqQXbxb",
    report: "https://drive.google.com/file/d/1KjZ71IhnsJdNqaukVpunL2nZkp6LDB2A/view",
    views: "92.2K",
    impressions: "325.6K",
    follows: "1,300"
  },
  {
    client: "U-Topia",
    topic: "AI",
    type: "Video on RT",
    date: "27/5/2025",
    title: "The AI Era Is Rewiring Us All w/@UCoinOfficial",
    url: "https://x.com/i/broadcasts/1MYxNwjneDNKw", 
    report: "https://drive.google.com/file/d/1ZamutJUzjNYrQh0YU01b-44AAjQn6Ff2/view",
    views: "73.6K",
    impressions: "266.3K",
    follows: "2,500"
  },
  {
    client: "Nektar",
    topic: "Defi",
    type: "Video on RT",
    date: "29/11/2024",
    title: "Why Staking is Reshaping DeFi #CryptoDaily w/@nektarnetwork",
    url: "https://x.com/i/broadcasts/1mrGmMYEyevGy",
    report: "https://drive.google.com/file/d/1nAZIpL93y6i9xuibLBmIoxoznaUeRP_p/view",
    views: "103.3K", 
    impressions: "316.4K",
    follows: "-"
  },
  {
    client: "DYOR Labs",
    topic: "Defi",
    type: "Video on M",
    date: "02/12/2024",
    title: "Altcoins to Watch Now! #CryptoDaily w/@DyorLabs_",
    url: "https://x.com/i/broadcasts/1eaJbavBoqrGX",
    report: "https://drive.google.com/file/d/1JqarPHdM4O5uLN40NIy2R-ZcH2qFkePW/view?usp=drive_link",
    views: "207.5K",
    impressions: "440.1K",
    follows: "800"
  },
  {
    client: "Solnic",
    topic: "Defi", 
    type: "Video on RT",
    date: "18/4/2025",
    title: "Top RWA & DeFi: Why \"Be Your Own Bank\" matters w/ @SolnicCoin",
    url: "https://x.com/i/broadcasts/1vOxwXpkRwqKB",
    report: "https://drive.google.com/file/d/1Pme2xSpHwu2UtUUHsN3jib4zyW83cb_a/view",
    views: "76.9K",
    impressions: "251.8K",
    follows: ""
  },
  {
    client: "Velo",
    topic: "Defi",
    type: "Video on M",
    date: "18/6/2025",
    title: "PARTNERED SHOW: Is PayFi the Future of Global Payments? w/@veloprotocol",
    url: "https://x.com/i/broadcasts/1eaKbWvjvLoGX", 
    report: "https://drive.google.com/file/d/17xM55dDa6G39qIUufuLiVtGG6Jbumsrt/view",
    views: "107.1K",
    impressions: "248.5K",
    follows: "4,800"
  },
  {
    client: "Open Loot",
    topic: "Gaming",
    type: "SS on M",
    date: "18/11/2024", 
    title: "Why Gamers Are Flocking to Web3 #CryptoDaily w/@OpenLoot",
    url: "https://x.com/i/spaces/1BdGYERELaAGX",
    report: "https://drive.google.com/file/d/1bCQDE053jAMVJvryWNH8MmZ8XHLMJ2Nj/view",
    views: "69.5K",
    impressions: "322.8K",
    follows: "-"
  },
  {
    client: "Vameon",
    topic: "Gaming",
    type: "SS on RT",
    date: "22/11/2024",
    title: "Where Are Gaming Tokens? #CryptoDaily w/@vameon69",
    url: "https://x.com/i/spaces/1LyGBgQOqDWJN",
    report: "https://drive.google.com/file/d/****************************-pXql/view?usp=drive_link",
    views: "50.4K", 
    impressions: "268.5K",
    follows: "-"
  },
  {
    client: "Le7el",
    topic: "Gaming",
    type: "Video on M",
    date: "26/11/2024",
    title: "The Metaverse Meta Revival #CryptoDaily w/@le7el_com",
    url: "https://x.com/i/broadcasts/1RDGlyqOXRjJL",
    report: "https://drive.google.com/file/d/1p2lWsMtWQjMpIj-xI73Caq-Nspp-Mub5/view",
    views: "176.8K",
    impressions: "471K", 
    follows: "-"
  },
  {
    client: "World of Dypians",
    topic: "Gaming",
    type: "Video on M",
    date: "28/11/2024",
    title: "Why Gamers Can't Ignore Blockchain #CryptoDaily w/@worldofdypians",
    url: "https://x.com/i/broadcasts/1jMJgBOqXmOGL",
    report: "https://drive.google.com/file/d/1NTcqNokH28RERVBYtBvGruVC-Wf6FZBl/view?usp=drive_link",
    views: "189.5K",
    impressions: "419K",
    follows: "3,200"
  },
  {
    client: "Pvp",
    topic: "Gaming", 
    type: "Video on M",
    date: "8/1/2025",
    title: "The Secret Behind Community Gaming w/@PvPGameHub",
    url: "https://x.com/i/broadcasts/1vAxROAXLzkKl",
    report: "https://drive.google.com/file/d/1qOgM0zZkdkDd8ckFRlLiI87jd80nS9t_/view?usp=drive_link",
    views: "Views/Listeners",
    impressions: "Impressions", 
    follows: "Follow Increase"
  }
];

// Helper functions
function parseDate(dateStr) {
  const [day, month, year] = dateStr.split('/');
  return new Date(year, month - 1, day).toISOString();
}

function parseMetric(value) {
  if (!value || value === '-' || value === '' || value.includes('Views') || value.includes('Impressions')) {
    return 0;
  }
  
  value = value.replace(/[~,]/g, '');
  
  if (value.includes('K')) {
    return Math.round(parseFloat(value.replace('K', '')) * 1000);
  } else if (value.includes('M')) {
    return Math.round(parseFloat(value.replace('M', '')) * 1000000);
  }
  
  return parseInt(value) || 0;
}

function mapTopic(topic) {
  const mapping = {
    'AI': ['ai'],
    'Defi': ['defi'], 
    'Gaming': ['gaming'],
    'Web2': ['web2']
  };
  return mapping[topic] || ['crypto'];
}

function mapShowType(showType) {
  if (showType.includes('SS on')) {
    return 'space';
  } else {
    return 'interview';
  }
}

// Transform data
function transformData(rawData) {
  return rawData
    .filter(item => item.client !== 'Client Name' && item.url && !item.url.includes('Views')) // Skip header and invalid rows
    .map(item => {
      const tags = [];
      
      // Add follow increase to tags
      const followIncrease = parseMetric(item.follows);
      if (followIncrease > 0) {
        tags.push(`follow_increase:${followIncrease}`);
      }
      
      // Add views to tags
      const views = parseMetric(item.views);
      if (views > 0) {
        tags.push(`views:${views}`);
      }
      
      return {
        content_link: item.url,
        content_title: item.title,
        content_account: item.client.trim(),
        content_created_date: parseDate(item.date),
        content_categories: mapTopic(item.topic),
        content_types: ['twitter'],
        twitter_content_type: mapShowType(item.type),
        twitter_impressions: parseMetric(item.impressions),
        content_tags: tags,
        content_description: `Report: ${item.report}`
      };
    });
}

// Main import function
async function importData() {
  try {
    const transformedData = transformData(rawData);
    
    console.log(`Importing ${transformedData.length} records...`);
    console.log('Sample record:', JSON.stringify(transformedData[0], null, 2));
    
    const { data, error } = await supabase
      .from('content_pieces')
      .insert(transformedData);
    
    if (error) {
      console.error('Error importing data:', error);
      return;
    }
    
    console.log('Successfully imported data:', data);
    
  } catch (err) {
    console.error('Import failed:', err);
  }
}

// Run the import
if (require.main === module) {
  importData();
}

module.exports = { transformData, parseMetric, parseDate };
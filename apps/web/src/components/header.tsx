"use client";
import Link from "next/link";
import { ModeToggle } from "./mode-toggle";
import { BarChart3, Zap } from "lucide-react";

export default function Header() {
  return (
    <header className="sticky top-0 z-50 w-full glass-effect border-b border-border/30">
      <div className="content-container">
        <div className="center-all justify-between h-20">
          {/* Brand */}
          <Link href="/" className="hover:opacity-90 transition-opacity duration-300">
            <span className="font-bold text-2xl tracking-tight text-attention-blue">ATTENTION</span>
          </Link>

          {/* Actions */}
          <div className="center-all gap-4">
            <ModeToggle />
          </div>
        </div>
      </div>
    </header>
  );
}

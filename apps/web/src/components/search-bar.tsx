"use client";

import { useState, useEffect } from "react";
import { Input } from "./ui/input";
import { Button } from "./ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "./ui/dropdown-menu";
import { 
  Search, 
  Filter, 
  SortDesc, 
  SortAsc,
  TrendingUp, 
  Calendar, 
  MessageCircle, 
  Users, 
  Twitter,
  Megaphone,
  Zap,
  Heart,
  Repeat2,
  Eye,
  Hash,
  X
} from "lucide-react";

interface Filters {
  contentType: string;
  category: string;
  sortBy: string;
  sortOrder: string;
}

interface SearchBarProps {
  onSearch: (query: string) => void;
  onFilter: (filterType: string, value: string) => void;
  onSort: (sortBy: string, sortOrder?: string) => void;
  placeholder?: string;
  filters: Filters;
  searchQuery: string;
}

const contentTypes = [
  { id: "all", label: "All Content", icon: <Zap className="w-4 h-4" /> },
  { id: "spaces", label: "Twitter Spaces", icon: <MessageCircle className="w-4 h-4" /> },
  { id: "tweets", label: "Tweets", icon: <Twitter className="w-4 h-4" /> },
  { id: "marketing", label: "Sponsored", icon: <Megaphone className="w-4 h-4" /> },
];

const categories = [
  { id: "all", label: "All Categories" },
  { id: "ai", label: "AI" },
  { id: "crypto", label: "Crypto" },
  { id: "defi", label: "DeFi" },
  { id: "gaming", label: "Gaming" },
  { id: "memecoin", label: "Memecoin" },
  { id: "nft", label: "NFT" },
  { id: "politics", label: "Politics" },
  { id: "news", label: "News" },
  { id: "markets", label: "Markets" },
  { id: "bitcoin", label: "Bitcoin" },
  { id: "ethereum", label: "Ethereum" },
  { id: "solana", label: "Solana" },
];

const sortOptions = [
  { id: "impressions", label: "Impressions", icon: <Eye className="w-4 h-4" /> },
  { id: "date", label: "Date", icon: <Calendar className="w-4 h-4" /> },
  { id: "likes", label: "Likes", icon: <Heart className="w-4 h-4" /> },
  { id: "retweets", label: "Retweets", icon: <Repeat2 className="w-4 h-4" /> },
];

export function SearchBar({ onSearch, onFilter, onSort, placeholder = "Search content...", filters, searchQuery }: SearchBarProps) {
  const [query, setQuery] = useState(searchQuery);
  const [debouncedQuery, setDebouncedQuery] = useState(searchQuery);

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, 300);

    return () => clearTimeout(timer);
  }, [query]);

  // Sync internal state with prop changes (for browser navigation)
  useEffect(() => {
    setQuery(searchQuery);
    setDebouncedQuery(searchQuery);
  }, [searchQuery]);

  // Call onSearch when debounced query changes
  useEffect(() => {
    onSearch(debouncedQuery);
  }, [debouncedQuery, onSearch]);

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.contentType !== "all") count++;
    if (filters.category !== "all") count++;
    return count;
  };

  const clearAllFilters = () => {
    onFilter("contentType", "all");
    onFilter("category", "all");
    setQuery("");
  };

  return (
    <div className="w-full space-y-4">
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-5 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
        <Input
          type="text"
          placeholder={placeholder}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="pl-14 pr-6 h-14 text-lg glass-effect border-0 focus:ring-2 focus:ring-primary/30 transition-all duration-300 font-medium placeholder:text-muted-foreground/60"
        />
      </div>

      {/* Filter Row */}
      <div className="flex gap-3 flex-wrap items-center">
        {/* Content Type Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="outline" 
              size="sm"
              className={`gap-2 glass-effect border-0 font-medium ${
                filters.contentType !== "all" ? "bg-primary/10 text-primary" : ""
              }`}
            >
              {contentTypes.find(t => t.id === filters.contentType)?.icon}
              {contentTypes.find(t => t.id === filters.contentType)?.label}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56 glass-effect border-0 shadow-xl">
            <div className="px-3 py-2 text-xs font-bold text-muted-foreground uppercase tracking-wider">
              Content Type
            </div>
            {contentTypes.map((type) => (
              <DropdownMenuItem 
                key={type.id}
                onClick={() => onFilter("contentType", type.id)}
                className={`gap-3 py-3 px-3 cursor-pointer rounded-lg mb-1 font-medium ${
                  filters.contentType === type.id ? "bg-primary/10 text-primary" : ""
                }`}
              >
                {type.icon}
                {type.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Category Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="outline" 
              size="sm"
              className={`gap-2 glass-effect border-0 font-medium ${
                filters.category !== "all" ? "bg-primary/10 text-primary" : ""
              }`}
            >
              <Hash className="w-4 h-4" />
              {categories.find(c => c.id === filters.category)?.label}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56 glass-effect border-0 shadow-xl max-h-80 overflow-y-auto">
            <div className="px-3 py-2 text-xs font-bold text-muted-foreground uppercase tracking-wider">
              Category
            </div>
            {categories.map((category) => (
              <DropdownMenuItem 
                key={category.id}
                onClick={() => onFilter("category", category.id)}
                className={`gap-3 py-3 px-3 cursor-pointer rounded-lg mb-1 font-medium ${
                  filters.category === category.id ? "bg-primary/10 text-primary" : ""
                }`}
              >
                <Hash className="w-4 h-4" />
                {category.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Sort Options */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="outline" 
              size="sm"
              className="gap-2 glass-effect border-0 font-medium"
            >
              {filters.sortOrder === "desc" ? <SortDesc className="w-4 h-4" /> : <SortAsc className="w-4 h-4" />}
              {sortOptions.find(s => s.id === filters.sortBy)?.label}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56 glass-effect border-0 shadow-xl">
            <div className="px-3 py-2 text-xs font-bold text-muted-foreground uppercase tracking-wider">
              Sort By
            </div>
            {sortOptions.map((option) => (
              <DropdownMenuItem 
                key={option.id}
                onClick={() => onSort(option.id)}
                className={`gap-3 py-3 px-3 cursor-pointer rounded-lg mb-1 font-medium ${
                  filters.sortBy === option.id ? "bg-primary/10 text-primary" : ""
                }`}
              >
                {option.icon}
                {option.label}
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator className="my-2" />
            <DropdownMenuItem 
              onClick={() => onSort(filters.sortBy, filters.sortOrder === "desc" ? "asc" : "desc")}
              className="gap-3 py-3 px-3 cursor-pointer rounded-lg font-medium"
            >
              {filters.sortOrder === "desc" ? <SortAsc className="w-4 h-4" /> : <SortDesc className="w-4 h-4" />}
              {filters.sortOrder === "desc" ? "Sort Ascending" : "Sort Descending"}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Clear Filters */}
        {(getActiveFiltersCount() > 0 || query.length > 0) && (
          <Button 
            variant="ghost" 
            size="sm"
            onClick={clearAllFilters}
            className="gap-2 text-muted-foreground hover:text-foreground"
          >
            <X className="w-4 h-4" />
            Clear ({getActiveFiltersCount() + (query.length > 0 ? 1 : 0)})
          </Button>
        )}
      </div>
    </div>
  );
}